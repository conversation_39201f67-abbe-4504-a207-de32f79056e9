#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def m1406a(i, char_array):
    """严格按照HopeHookEntrance.java中的解密逻辑"""
    length = len(char_array)
    for i2 in range(length):
        c = char_array[i2]
        
        # 根据位置模7的余数选择不同的异或值
        remainder = i2 % 7
        if remainder == 0 or remainder == 2:
            i3 = 47
        elif remainder == 1:
            i3 = 81
        elif remainder == 3:
            i3 = 40
        elif remainder == 4:
            i3 = 106
        elif remainder == 5:
            i3 = 86
        else:  # remainder == 6
            i3 = 10
        
        # 执行异或操作
        char_array[i2] = chr(ord(c) ^ (i ^ i3))
    
    return ''.join(char_array)

def m1409a(string):
    """转换字符串为字符数组"""
    char_array = list(string)
    if len(char_array) < 2:
        char_array[0] = chr(ord(char_array[0]) ^ ord('\n'))
    return char_array

# 直接执行解密
print("开始解密HopeHookEntrance字符串...")

# 第一个加密字符串
str1 = "(G.)~W\u001c)Z3\r'\\)co@\u0003}\u0001d gP\u0011#P'<k@@(G.)~W\u001c)Z3\n(G.)~W\u001c)Z3"

# 第二个加密字符串
str2 = "fE'\b*W;*#P\u001d#"

print(f"第一个字符串长度: {len(str1)}")
print(f"第二个字符串长度: {len(str2)}")

# 根据原代码逻辑分析分割点
str_arr = [""] * 6
current_str = str1
pos = 0
length = len(current_str)
segment_len = 10  # 初始长度
str_index = 0

print("\n开始分割和解密:")

while str_index < 6:
    # 提取当前段
    end_pos = pos + segment_len
    if end_pos > length:
        end_pos = length
    
    segment = current_str[pos:end_pos]
    print(f"段 {str_index}: 位置[{pos}:{end_pos}] 长度{len(segment)}")
    print(f"  内容: {repr(segment)}")
    
    # 解密当前段
    char_array = m1409a(segment)
    key = 100 if str_index < 3 else 103  # 前3个用100，后面的用103
    decrypted = m1406a(key, char_array)
    str_arr[str_index] = decrypted
    print(f"  解密结果: {repr(decrypted)}")
    
    str_index += 1
    
    # 更新位置和下一段长度
    pos = end_pos
    if pos >= length:
        # 切换到第二个字符串
        if current_str == str1:
            current_str = str2
            length = len(current_str)
            pos = 0
            segment_len = 3  # 第二个字符串的初始长度
            print("  切换到第二个字符串")
        else:
            break
    else:
        # 下一段长度由当前位置的字符ASCII值决定
        segment_len = ord(current_str[pos])
        print(f"  下一段长度: {segment_len}")
        pos += 1  # 跳过长度指示字符

print("\n" + "="*50)
print("最终解密结果:")
labels = ["库名称", "路径1", "搜索路径", "替换路径", "文件扩展", "目标路径"]
for i, s in enumerate(str_arr):
    if s:
        print(f"{labels[i]}: {repr(s)}")

print("\n这是HopeHookEntrance的Hook相关配置字符串。")
